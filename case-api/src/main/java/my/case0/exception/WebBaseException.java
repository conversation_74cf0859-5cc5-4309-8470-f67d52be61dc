package my.case0.exception;

import lombok.Getter;
import my.case0.model.RespMeta;

@Getter
public class WebBaseException extends RuntimeException {

    private final int status;
    private final String msg;

    public WebBaseException(String msg) {
        super(msg);
        this.status = 500;
        this.msg = msg;
    }

    public WebBaseException(int status, String msg) {
        super(msg);
        this.status = status;
        this.msg = msg;
    }


    public WebBaseException(String msg, Throwable throwable) {
        super(msg, throwable);
        this.status = 500;
        this.msg = msg;
    }

    public WebBaseException(int status, String msg, Throwable throwable) {
        super(msg, throwable);
        this.status = status;
        this.msg = msg;
    }

    public WebBaseException(RespMeta code) {
        this(code.getStatus(), code.getMsg());
    }

    public WebBaseException(RespMeta code, Throwable throwable) {
        this(code.getStatus(), code.getMsg(), throwable);
    }

}
