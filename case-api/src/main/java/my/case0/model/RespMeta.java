package my.case0.model;

import lombok.Getter;

@Getter
public enum RespMeta {

    SUCCESS(200, "请求成功"),

    NOT_LOGIN(444, "未登陆或登陆过期"),
    NO_AUTH(445, "没有访问系统的权限"),
    NOT_REGISTER(446, "未注册"),


    PARAM_ERROR(1000, "参数错误"),
    TIMEOUT(1001, "网络繁忙，请稍后再试"),
    NO_REGISTER(1002, "首次登录时必须有手机号"),

    LOGIN_FAIL(2001, "登陆失败"),
    REGISTER_FAIL_REPEAT_USERNAME(2002, "用户名重复"),
    NO_USER(2003, "傲爸妈用户不存在"),
    REPEAT_PWD_ERROR(2004, "密码错误超过当日上限，请明日再试"),
    PWD_ERROR(2005, "账户或密码错误"),
    USER_FROZEN(2006, "用户被冻结"),
    PASSWORD_FAILED(2008, "密码错误"),

    NO_CLAZZ(3001, "没有分班，请先进行分班"),
    ;

    private final int status;
    private final String msg;

    RespMeta(int status, String msg) {
        this.status = status;
        this.msg = msg;
    }

}
