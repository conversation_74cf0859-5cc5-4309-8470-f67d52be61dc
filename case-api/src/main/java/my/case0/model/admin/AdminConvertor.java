package my.case0.model.admin;

import my.case0.entity.Admin;

public class AdminConvertor {

    public static ListResp toListResp(Admin admin) {
        ListResp resp = new ListResp();
        resp.setId(admin.getId());
        resp.setNickname(admin.getNickname());
        resp.setAccount(admin.getAccount());
        resp.setAdminRole(admin.getAdminRole());
        resp.setAdminStatus(admin.getAdminStatus());
        resp.setCreatedTime(admin.getCreatedTime());
        resp.setLastLoginTime(admin.getLastLoginTime());
        return resp;
    }
}
