package my.case0.util;

import org.springframework.context.ApplicationContext;

public class BeanUtils {

    private static ApplicationContext applicationContext;

    public static void init(ApplicationContext applicationContext) {
        BeanUtils.applicationContext = applicationContext;
    }

    public static <T> T getBean(Class<T> clazz) {
        return applicationContext.getBean(clazz);
    }

    public static <T> T getBean(String name, Class<T> clazz) {
        return applicationContext.getBean(name, clazz);
    }

    public static boolean containsBean(String name) {
        return applicationContext.containsBean(name);
    }
}
