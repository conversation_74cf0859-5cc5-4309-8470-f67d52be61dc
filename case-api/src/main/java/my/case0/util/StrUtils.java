package my.case0.util;

import java.util.List;

public class StrUtils {

    public static final List<String> endpoints = List.of("。", "？", "！", "；", "：", ",", ".", "?", "!", ":", ";");

    public static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }

    public static boolean isNotEmpty(String str) {
        return !isEmpty(str);
    }

    public static String underlineToCamel(String word) {
        if (word == null) {
            return null;
        }
        String[] split = word.split("_");
        StringBuilder sb = new StringBuilder(word.length());
        for (String s : split) {
            char[] chars = s.toCharArray();
            if(chars[0] >='a' && chars[0] <= 'z'){
                chars[0] -= 32;
            }
            sb.append(chars);
        }
        return sb.toString();
    }

    public static String camelToUnderline(String name) {
        StringBuilder buf = new StringBuilder();
        for (int i = 0; i < name.length(); ++i) {
            char ch = name.charAt(i);
            if (ch >= 'A' && ch <= 'Z') {
                char newChar = (char) (ch + 32);
                if (i > 0) {
                    buf.append('_');
                }
                buf.append(newChar);
            } else {
                buf.append(ch);
            }
        }
        return buf.toString();
    }

}
