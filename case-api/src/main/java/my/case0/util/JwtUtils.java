package my.case0.util;

import com.auth0.jwt.JWT;
import com.auth0.jwt.JWTVerifier;
import com.auth0.jwt.algorithms.Algorithm;
import com.auth0.jwt.interfaces.Claim;
import com.auth0.jwt.interfaces.DecodedJWT;

import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.Map;

public class JwtUtils {

    private static String token;
    private static Algorithm algorithm;
    private static Map<String, Object> header;
    private static JWTVerifier verifier;

    public static void init(String token) {
        JwtUtils.token = token;
        algorithm = Algorithm.HMAC256(token);
        header = Map.of("typ", "JWT", "alg", "HS256");
        verifier = JWT.require(algorithm).build();
    }

    public static String createAdminToken(Integer userId, Byte role) {
        LocalDateTime expiredTime = LocalDateTime.now().plusDays(1);
        return createToken(Map.of("userId", userId.toString(), "role", role.toString()), expiredTime);
    }

    public static String createToken(Map<String, Object> param, LocalDateTime expireDate) {
        return createToken(param, DateUtils.asDate(expireDate));
    }

    public static String createToken(Map<String, Object> params, Date expireTime) {
        var builder = JWT.create().withHeader(header);
        // 设置 载荷 Payload
        builder.withClaim("params", params);
        return builder
                // 生成签名的时间
                .withIssuedAt(new Date())
                // 签名过期的时间
                .withExpiresAt(expireTime)
                // 签名Signature
                .sign(algorithm);
    }

    public static Map<String, Object> verifyToken(String token) {
        DecodedJWT jwt = verifier.verify(token);
        Map<String, Claim> claims = jwt.getClaims();
        Claim claim = claims.get("params");
        return claim.asMap();
    }

    public static String hashPwd(String pwd) {
        try {
            var md = MessageDigest.getInstance("SHA-256");
            byte[] hashedBytes = md.digest((token + pwd).getBytes(StandardCharsets.UTF_8));
            StringBuilder sb = new StringBuilder();
            for (byte b : hashedBytes) {
                sb.append(String.format("%02x", b));
            }
            return sb.toString();
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException("无法找到SHA-256算法", e);
        }
    }

    public static void main(String[] args) {
        System.out.println(hashPwd("admin1234"));
    }
}
