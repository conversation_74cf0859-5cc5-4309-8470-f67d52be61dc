package my.case0.util;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.Date;
import java.util.concurrent.TimeUnit;

public class DateUtils {

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public static final DateTimeFormatter DATE_NONE_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    public static final DateTimeFormatter TIME_FORMATTER = DateTimeFormatter.ofPattern("HH:mm:ss");
    public static final DateTimeFormatter DATE_FORMATTER_MD = DateTimeFormatter.ofPattern("MM-dd");

    public static final ZoneOffset SYS_TIME_ZONE = ZoneOffset.of("+8");

    public static Date asDate(LocalDate localDate) {
        return Date.from(localDate.atStartOfDay().atZone(ZoneId.systemDefault()).toInstant());
    }

    public static Date asDate(LocalDateTime localDateTime) {
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    public static LocalDate asLocalDate(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static LocalDateTime asLocalDateTime(Date date) {
        return Instant.ofEpochMilli(date.getTime()).atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    public static String format(LocalDate localDate) {
        if (localDate == null) {
            return "";
        }
        return localDate.format(DateTimeFormatter.ISO_LOCAL_DATE);
    }

    public static String format(LocalDate localDate, DateTimeFormatter formatter) {
        if (localDate == null) {
            return "";
        }
        return localDate.format(formatter);
    }

    public static String format(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return "";
        }
        return localDateTime.format(DATE_TIME_FORMATTER);
    }

    public static LocalDateTime parseDateTime(String dateStr) {
        return LocalDateTime.parse(dateStr, DATE_TIME_FORMATTER);
    }

    public static LocalDateTime parseDateTime(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, SYS_TIME_ZONE);
    }

    public static LocalDate parseDate(String dateStr) {
        return LocalDate.parse(dateStr, DATE_FORMATTER);
    }

    public static LocalDate parseDate(long timestamp) {
        Instant instant = Instant.ofEpochMilli(timestamp);
        return LocalDateTime.ofInstant(instant, SYS_TIME_ZONE).toLocalDate();
    }

    public static LocalDate parseDate(String dateStr, DateTimeFormatter formatter) {
        return LocalDate.parse(dateStr, formatter);
    }

    public static LocalTime parseTime(String timeStr) {
        return LocalTime.parse(timeStr, TIME_FORMATTER);
    }

    public static LocalTime parseTime(long timestamp) {
        return LocalTime.ofInstant(Instant.ofEpochMilli(timestamp), SYS_TIME_ZONE);
    }

    /**
     * 将毫秒数转换成秒数
     * @param millis 毫秒数
     * @return 秒数
     */
    public static long toSeconds(long millis) {
        return TimeUnit.SECONDS.convert(millis, TimeUnit.MILLISECONDS);
    }

    public static Long toMillis(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.toInstant(SYS_TIME_ZONE).toEpochMilli();
    }

    public static long toMillis(LocalDate localDate) {
        return localDate.atStartOfDay(SYS_TIME_ZONE).toInstant().toEpochMilli();
    }

    public static long toMillis(LocalTime localTime) {
        return localTime.atDate(LocalDate.now()).toInstant(SYS_TIME_ZONE).toEpochMilli();
    }

    public static long toSeconds(LocalDateTime localDateTime) {
        return localDateTime.toEpochSecond(ZoneOffset.of("+8"));
    }

    public static LocalDateTime secondToLocalDateTime(long timestamp) {
        Instant instant = Instant.ofEpochSecond(timestamp);
        ZoneId zone = ZoneId.systemDefault();
        return LocalDateTime.ofInstant(instant, zone);
    }

    public static int days(LocalDateTime startDate, LocalDateTime endDate) {
        return days(startDate.toLocalDate(), endDate.toLocalDate());
    }

    public static int days(LocalDate startDate, LocalDate endDate) {
        return (int) startDate.until(endDate, ChronoUnit.DAYS) + 1;
    }

    public static long seconds(LocalDateTime startDate, LocalDateTime endDate) {
        return startDate.until(endDate, ChronoUnit.SECONDS);
    }
}
