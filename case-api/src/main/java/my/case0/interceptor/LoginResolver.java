package my.case0.interceptor;

import lombok.extern.slf4j.Slf4j;
import my.case0.model.UserInfo;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.MethodParameter;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.support.WebDataBinderFactory;
import org.springframework.web.context.request.NativeWebRequest;
import org.springframework.web.method.support.HandlerMethodArgumentResolver;
import org.springframework.web.method.support.ModelAndViewContainer;

@Slf4j
@Component
public class LoginResolver implements HandlerMethodArgumentResolver {

    @Value("${spring.profiles.active}")
    private String profiles;

    @Override
    public boolean supportsParameter(MethodParameter parameter) {
        return parameter.hasParameterAnnotation(Login.class);
    }

    @Override
    public Object resolveArgument(@NotNull MethodParameter parameter, ModelAndViewContainer container,
                                  NativeWebRequest request, WebDataBinderFactory factory) {
        Object userInfo = request.getAttribute("userInfo", NativeWebRequest.SCOPE_REQUEST);
        if (userInfo == null) {
            return null;
        }
        if (parameter.getParameterType().isAssignableFrom(UserInfo.class)) {
            return userInfo;
        }
        return null;
    }

}
