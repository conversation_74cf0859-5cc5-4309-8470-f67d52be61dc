package my.case0.interceptor;

import com.auth0.jwt.exceptions.TokenExpiredException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import my.case0.model.CommResp;
import my.case0.model.RespMeta;
import my.case0.model.UserInfo;
import my.case0.util.JsonUtils;
import my.case0.util.JwtUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Map;

@Slf4j
@Component
public class AuthInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, @NotNull HttpServletResponse response, @NotNull Object handler) throws IOException {
        if (HttpMethod.OPTIONS.name().equals(request.getMethod())) {
            return true;
        }
        if (!(handler instanceof HandlerMethod handlerMethod)) {
            return true;
        }
        Map<String, Object> tokenParams = parseToken(request);
        if (tokenParams != null) {
            Object userId = tokenParams.get("userId");
            Object role = tokenParams.get("role");
            if (!"1".equals(role) && handlerMethod.hasMethodAnnotation(Admin.class)) {
                resp(response);
                return false;
            }
            request.setAttribute("userInfo", new UserInfo(Integer.valueOf(userId.toString()), Byte.valueOf(role.toString())));
        } else if (handlerMethod.hasMethodAnnotation(Admin.class)) {
            resp(response);
            return false;
        }
        return true;
    }

    protected Map<String, Object> parseToken(HttpServletRequest request) {
        String token = request.getHeader(HttpHeaders.AUTHORIZATION);
        if (!StringUtils.hasLength(token)) {
            return null;
        }
        token = token.trim();
        Map<String, Object> tokenParams = null;
        try {
            tokenParams = JwtUtils.verifyToken(token);
        } catch (TokenExpiredException e) {
            log.info("Token过期");
        } catch (NumberFormatException e) {
            log.error("解析用户ID错误", e);
        } catch (Exception e) {
            log.error("解析Token错误", e);
        }
        return tokenParams;
    }

    protected void resp(HttpServletResponse response) throws IOException {
        response.setStatus(HttpStatus.OK.value());
        response.setContentType(MediaType.APPLICATION_JSON_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        response.getWriter().write(JsonUtils.formatObjToJson(CommResp.warning(RespMeta.NOT_LOGIN)));
    }
}
