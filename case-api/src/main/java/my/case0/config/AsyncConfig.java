package my.case0.config;

import lombok.extern.slf4j.Slf4j;
import my.case0.exception.NothingValueException;
import org.jetbrains.annotations.NotNull;
import org.springframework.aop.interceptor.AsyncUncaughtExceptionHandler;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.AsyncConfigurer;
import org.springframework.scheduling.annotation.EnableAsync;

import java.lang.reflect.Method;

@EnableAsync
@Configuration
@Slf4j
public class AsyncConfig implements AsyncConfigurer {


    @Override
    public AsyncUncaughtExceptionHandler getAsyncUncaughtExceptionHandler() {
        return new AsyncUncaughtExceptionHandler() {
            @Override
            public void handleUncaughtException(@NotNull Throwable ex, @NotNull Method method, @NotNull Object... params) {
                if (ex instanceof NothingValueException nothingValueException) {
                    if (nothingValueException.getMsg() != null) {
                        log.error(nothingValueException.getMsg());
                    }
                } else if (log.isErrorEnabled()) {
                    log.error("Unexpected exception occurred invoking async method: {}", method, ex);
                }
            }
        };
    }
}
