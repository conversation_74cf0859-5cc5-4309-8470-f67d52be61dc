package my.case0.config;

import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.PostConstruct;
import my.case0.util.BeanUtils;
import my.case0.util.FileUtils;
import my.case0.util.JsonUtils;
import my.case0.util.JwtUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.io.ResourceLoader;

@Configuration
public class UtilsConfig {

    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private ApplicationContext applicationContext;
    @Autowired
    private ResourceLoader resourceLoader;
    @Value("${app.auth.token}")
    private String token;

    @PostConstruct
    public void init() {
        JsonUtils.init(objectMapper);
        BeanUtils.init(applicationContext);
        FileUtils.init(resourceLoader);
        JwtUtils.init(token);
    }

}
