package my.case0.config;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.deser.std.StdDeserializer;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;
import my.case0.util.DateUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;

import java.io.IOException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;

@Configuration
public class JacksonConfig {

    @Bean
    public Converter<String, LocalDateTime> localDateTimeConverter() {
        return new Converter<String, LocalDateTime>() {
            @Override
            public LocalDateTime convert(@NotNull String source) {
                return DateUtils.parseDateTime(Long.parseLong(source));
            }
        };
    }

    @Bean
    public Converter<String, LocalDate> localDateConverter() {
        return new Converter<String, LocalDate>() {
            @Override
            public LocalDate convert(@NotNull String source) {
                return DateUtils.parseDate(Long.parseLong(source));
            }
        };
    }

    @Bean
    public Converter<String, LocalTime> localTimeConverter() {
        return new Converter<String, LocalTime>() {
            @Override
            public LocalTime convert(@NotNull String source) {
                return DateUtils.parseTime(Long.parseLong(source));
            }
        };
    }

    @Bean
    public Jackson2ObjectMapperBuilderCustomizer jackson2ObjectMapperBuilderCustomizer() {

        return builder -> {
            builder.serializerByType(LocalDateTime.class, new LocalDateTimeSerialize(LocalDateTime.class));
            builder.deserializerByType(LocalDateTime.class, new LocalDateTimeDeserializer(LocalDateTime.class));
            builder.serializerByType(LocalDate.class, new LocalDateSerialize(LocalDate.class));
            builder.deserializerByType(LocalDate.class, new LocalDateDeserializer(LocalDate.class));
            builder.serializerByType(LocalTime.class, new LocalTimeSerialize(LocalTime.class));
            builder.deserializerByType(LocalTime.class, new LocalTimeDeserializer(LocalTime.class));
        };
    }

    public static class LocalDateTimeDeserializer extends StdDeserializer<LocalDateTime> {

        protected LocalDateTimeDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public LocalDateTime deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
            long timestamp = p.getValueAsLong();
            return DateUtils.parseDateTime(timestamp);
        }
    }

    public static class LocalDateTimeSerialize extends StdSerializer<LocalDateTime> {

        public LocalDateTimeSerialize(Class<LocalDateTime> t) {
            super(t);
        }

        @Override
        public void serialize(LocalDateTime localDateTime, JsonGenerator jsonGenerator,
                              SerializerProvider serializerProvider) throws IOException {
            jsonGenerator.writeNumber(DateUtils.toMillis(localDateTime));
        }
    }

    public static class LocalDateDeserializer extends StdDeserializer<LocalDate> {

        protected LocalDateDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public LocalDate deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
            long timestamp = p.getValueAsLong();
            return DateUtils.parseDate(timestamp);
        }
    }

    public static class LocalDateSerialize extends StdSerializer<LocalDate> {

        public LocalDateSerialize(Class<LocalDate> t) {
            super(t);
        }

        @Override
        public void serialize(LocalDate localDate, JsonGenerator jsonGenerator,
                              SerializerProvider serializerProvider) throws IOException {
            jsonGenerator.writeNumber(DateUtils.toMillis(localDate));
        }
    }

    public static class LocalTimeDeserializer extends StdDeserializer<LocalTime> {

        protected LocalTimeDeserializer(Class<?> vc) {
            super(vc);
        }

        @Override
        public LocalTime deserialize(JsonParser p, DeserializationContext ctx) throws IOException {
            long timestamp = p.getValueAsLong();
            return DateUtils.parseTime(timestamp);
        }
    }

    public static class LocalTimeSerialize extends StdSerializer<LocalTime> {

        public LocalTimeSerialize(Class<LocalTime> t) {
            super(t);
        }

        @Override
        public void serialize(LocalTime localTime, JsonGenerator jsonGenerator,
                              SerializerProvider serializerProvider) throws IOException {
            jsonGenerator.writeNumber(DateUtils.toMillis(localTime));
        }
    }

}
