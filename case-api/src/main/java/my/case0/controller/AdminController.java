package my.case0.controller;

import com.github.benmanes.caffeine.cache.Cache;
import com.google.code.kaptcha.Producer;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import my.case0.interceptor.Admin;
import my.case0.interceptor.Login;
import my.case0.model.CommResp;
import my.case0.model.UserInfo;
import my.case0.model.admin.ListReq;
import my.case0.model.admin.ListResp;
import my.case0.model.admin.LoginReq;
import my.case0.model.admin.LoginResp;
import my.case0.service.AdminService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.util.List;

/**
 * 管理员控制器
 */
@RestController
@RequestMapping("api/admin")
@Slf4j
public class AdminController {

    @Autowired
    private AdminService adminService;
    @Autowired
    private Producer producer;
    @Autowired
    @Qualifier("loginCache")
    private Cache<String, String> loginCache;

    /**
     * 图片验证码
     */
    @GetMapping("image/code")
    public void imageCode(
            @RequestParam("code") String code,
            HttpServletResponse response
    ) throws IOException {
        response.setContentType("image/jpeg");
        //生成文字验证码
        String text = producer.createText();
        //个位数字相加
        String s1 = text.substring(0, 2);
        String s2 = text.substring(2, 4);
        int count = Integer.parseInt(s1) + Integer.parseInt(s2);
        //生成图片验证码
        BufferedImage image = producer.createImage(s1 + "+" + s2 + "=?");
        ImageIO.write(image, "jpg", response.getOutputStream());
        loginCache.put(code, String.valueOf(count));
    }

    @PostMapping("login")
    public CommResp<LoginResp> login(
            @RequestBody LoginReq req
    ) {
        return adminService.login(req);
    }

    @GetMapping("list")
    @Admin
    public CommResp<List<ListResp>> list(
            @Login UserInfo userInfo,
            ListReq req
    ) {
        return adminService.list(req);
    }

}
